/* body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f9fbfc;
  margin: 0;
  padding: 0;
}

.container {
  padding: 2rem;
  max-width: 700px;
  margin: 40px auto;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.title {
  text-align: center;
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  font-weight: 600;
  margin-bottom: 6px;
  color: #34495e;
}

.form-control,
.form-control-file {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-sizing: border-box;
  font-size: 1rem;
  resize: vertical;
}

.btn-send {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 6px;
  cursor: pointer;
  width: 100%;
  margin-top: 1rem;
  transition: background-color 0.2s ease-in-out;
}

.btn-send:hover {
  background-color: #2980b9;
}

.result {
  margin-top: 2rem;
  background-color: #ecf0f1;
  padding: 1rem;
  border-radius: 8px;
  color: #2c3e50;
} */

/* .container {
  padding: 40px;
  max-width: 1200px;
  margin: auto;
  font-family: 'Segoe UI', sans-serif;
} */
.container {
  max-width: 800px;
  background-color: #ffffff;
  margin: 40px auto;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  text-align: center;
  height: 500px !important;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Prevent outer scroll */
}

.sheetwa-wrapper {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  flex: 1;
  overflow: hidden;
}
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e0e6f0;
  padding-bottom: 0.5rem;
}

.header img {
  width: 36px;
  height: 36px;
  margin-right: 12px;
}

.header h2 {
  font-size: 1.4rem;
  margin: 0;
}

.badge {
  background-color: #ff9f00;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
  padding: 4px 12px;
  border-radius: 20px;
  margin-left: auto;
}



.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 55%;
}

.send-btn {
  padding: 10px 20px;
  font-weight: bold;
  background-color: rgb(48, 48, 249);
  color: white;
  border-radius: 10px;
  border: 1px solid white;
}
button-group{
  gap: 55%;
}




@media (max-width: 600px) {
  .container {
    height: auto !important;
    margin: 20px 10px;
    padding: 16px;
    border-radius: 10px;
    box-shadow: none;
  }

  .sheetwa-wrapper {
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
  }

  .header {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .header img {
    margin: 0 auto 10px;
  }

  .header h2 {
    font-size: 1.2rem;
  }

  .badge {
    margin-left: 0;
    margin-top: 10px;
  }

  .button-group {
    flex-direction: column;
    align-items: stretch;
    justify-content: center;
    gap: 12px;
    padding: 10px 0;
  }

  .send-btn {
    width: 100%;
    font-size: 15px;
    padding: 12px;
  }
}