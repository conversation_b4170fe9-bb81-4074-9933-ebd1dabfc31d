{"version": 3, "sources": ["../../../../../../node_modules/ngx-quill/fesm2022/ngx-quill-config.mjs", "../../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../../node_modules/ngx-quill/fesm2022/ngx-quill.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, NgModule, makeEnvironmentProviders } from '@angular/core';\nconst defaultModules = {\n  toolbar: [['bold', 'italic', 'underline', 'strike'],\n  // toggled buttons\n  ['blockquote', 'code-block'], [{\n    header: 1\n  }, {\n    header: 2\n  }],\n  // custom button values\n  [{\n    list: 'ordered'\n  }, {\n    list: 'bullet'\n  }], [{\n    script: 'sub'\n  }, {\n    script: 'super'\n  }],\n  // superscript/subscript\n  [{\n    indent: '-1'\n  }, {\n    indent: '+1'\n  }],\n  // outdent/indent\n  [{\n    direction: 'rtl'\n  }],\n  // text direction\n  [{\n    size: ['small', false, 'large', 'huge']\n  }],\n  // custom dropdown\n  [{\n    header: [1, 2, 3, 4, 5, 6, false]\n  }], [{\n    color: []\n  }, {\n    background: []\n  }],\n  // dropdown with defaults from theme\n  [{\n    font: []\n  }], [{\n    align: []\n  }], ['clean'],\n  // remove formatting button\n  ['link', 'image', 'video'],\n  // link and image, video\n  ['table']]\n};\nconst QUILL_CONFIG_TOKEN = new InjectionToken('config', {\n  providedIn: 'root',\n  factory: () => ({\n    modules: defaultModules\n  })\n});\n\n/**\n * This `NgModule` provides a global Quill config on the root level, e.g., in `AppModule`.\n * But this eliminates the need to import the entire `ngx-quill` library into the main bundle.\n * The `quill-editor` itself may be rendered in any lazy-loaded module, but importing `QuillModule`\n * into the `AppModule` will bundle the `ngx-quill` into the vendor.\n */\nclass QuillConfigModule {\n  static forRoot(config) {\n    return {\n      ngModule: QuillConfigModule,\n      providers: [{\n        provide: QUILL_CONFIG_TOKEN,\n        useValue: config\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function QuillConfigModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuillConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: QuillConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillConfigModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Provides Quill configuration at the root level:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideQuillConfig(...)]\n * });\n * ```\n */\nconst provideQuillConfig = config => makeEnvironmentProviders([{\n  provide: QUILL_CONFIG_TOKEN,\n  useValue: config\n}]);\n\n/*\n * Public API Surface of ngx-quill/config\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QUILL_CONFIG_TOKEN, QuillConfigModule, defaultModules, provideQuillConfig };\n", "/**\n * @license Angular v19.2.6\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError as _RuntimeError, ɵgetOutputDestroyRef as _getOutputDestroyRef, Injector, effect, untracked, ɵmicrotaskEffect as _microtaskEffect, assertNotInReactiveContext, signal, computed, PendingTasks, resource } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  source;\n  destroyed = false;\n  destroyRef = inject(DestroyRef);\n  constructor(source) {\n    this.source = source;\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new _RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi\n */\nfunction outputToObservable(ref) {\n  const destroyRef = _getOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\nfunction toObservableMicrotask(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = _microtaskEffect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  typeof ngDevMode !== 'undefined' && ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new _RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new _RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @experimental\n */\nfunction pendingUntilEvent(injector) {\n  if (injector === undefined) {\n    assertInInjectionContext(pendingUntilEvent);\n    injector = inject(Injector);\n  }\n  const taskService = injector.get(PendingTasks);\n  return sourceObservable => {\n    return new Observable(originalSubscriber => {\n      // create a new task on subscription\n      const removeTask = taskService.add();\n      let cleanedUp = false;\n      function cleanupTask() {\n        if (cleanedUp) {\n          return;\n        }\n        removeTask();\n        cleanedUp = true;\n      }\n      const innerSubscription = sourceObservable.subscribe({\n        next: v => {\n          originalSubscriber.next(v);\n          cleanupTask();\n        },\n        complete: () => {\n          originalSubscriber.complete();\n          cleanupTask();\n        },\n        error: e => {\n          originalSubscriber.error(e);\n          cleanupTask();\n        }\n      });\n      innerSubscription.add(() => {\n        originalSubscriber.unsubscribe();\n        cleanupTask();\n      });\n      return innerSubscription;\n    });\n  };\n}\nfunction rxResource(opts) {\n  opts?.injector || assertInInjectionContext(rxResource);\n  return resource({\n    ...opts,\n    loader: undefined,\n    stream: params => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      params.abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function send(value) {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      }\n      sub = opts.loader(params).subscribe({\n        next: value => send({\n          value\n        }),\n        error: error => send({\n          error\n        }),\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new Error('Resource completed before producing a value')\n            });\n          }\n          params.abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }\n  });\n}\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal, toObservableMicrotask as ɵtoObservableMicrotask };\n", "import { defaultModules, QUILL_CONFIG_TOKEN } from 'ngx-quill/config';\nconst _c0 = [[[\"\", \"above-quill-editor-toolbar\", \"\"]], [[\"\", \"quill-editor-toolbar\", \"\"]], [[\"\", \"below-quill-editor-toolbar\", \"\"]]];\nconst _c1 = [\"[above-quill-editor-toolbar]\", \"[quill-editor-toolbar]\", \"[below-quill-editor-toolbar]\"];\nfunction QuillEditorComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 0);\n  }\n}\nfunction QuillEditorComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 0);\n  }\n}\nexport * from 'ngx-quill/config';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, input, EventEmitter, signal, ElementRef, ChangeDetectorRef, PLATFORM_ID, Renderer2, NgZone, DestroyRef, SecurityContext, Output, Directive, forwardRef, ViewEncapsulation, Component, NgModule } from '@angular/core';\nimport { isPlatformServer } from '@angular/common';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { Observable, defer, forkJoin, of, isObservable, fromEvent, Subscription } from 'rxjs';\nimport { shareReplay, map, tap, mergeMap, debounceTime } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nconst getFormat = (format, configFormat) => {\n  const passedFormat = format || configFormat;\n  return passedFormat || 'html';\n};\nconst raf$ = () => {\n  return new Observable(subscriber => {\n    const rafId = requestAnimationFrame(() => {\n      subscriber.next();\n      subscriber.complete();\n    });\n    return () => cancelAnimationFrame(rafId);\n  });\n};\nclass QuillService {\n  constructor() {\n    this.config = inject(QUILL_CONFIG_TOKEN) || {\n      modules: defaultModules\n    };\n    this.quill$ = defer(async () => {\n      if (!this.Quill) {\n        // Quill adds event listeners on import:\n        // https://github.com/quilljs/quill/blob/develop/core/emitter.js#L8\n        // We want to use the unpatched `addEventListener` method to ensure all event\n        // callbacks run outside of zone.\n        // Since we don't yet know whether `zone.js` is used, we simply save the value\n        // to restore it later.\n        // We can use global `document` because we execute it only in the browser.\n        const maybePatchedAddEventListener = document.addEventListener;\n        // There are two types of Angular applications:\n        // 1) default (with zone.js)\n        // 2) zoneless\n        // Developers can avoid importing the `zone.js` package and inform Angular that\n        // they are responsible for running change detection manually.\n        // This can be done using `provideZonelessChangeDetection()`.\n        // We fall back to `document.addEventListener` if `__zone_symbol__addEventListener`\n        // is not defined, which indicates that `zone.js` is not imported.\n        // The `__zone_symbol__addEventListener` is essentially the native DOM API,\n        // unpatched by zone.js, meaning it does not go through the `zone.js` task lifecycle.\n        document.addEventListener = document['__zone_symbol__addEventListener'] || document.addEventListener;\n        const {\n          Quill\n        } = await import('./ngx-quill-quill-CUw8Q_m0.mjs');\n        document.addEventListener = maybePatchedAddEventListener;\n        this.Quill = Quill;\n      }\n      // Only register custom options and modules once\n      this.config.customOptions?.forEach(customOption => {\n        const newCustomOption = this.Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        this.Quill.register(newCustomOption, true, this.config.suppressGlobalRegisterWarning);\n      });\n      // Use `Promise` directly to avoid bundling `firstValueFrom`.\n      return new Promise(resolve => {\n        this.registerCustomModules(this.Quill, this.config.customModules, this.config.suppressGlobalRegisterWarning).subscribe(resolve);\n      });\n    }).pipe(shareReplay({\n      bufferSize: 1,\n      refCount: false\n    }));\n    // A list of custom modules that have already been registered,\n    // so we don’t need to await their implementation.\n    this.registeredModules = new Set();\n  }\n  getQuill() {\n    return this.quill$;\n  }\n  /** @internal */\n  beforeRender(Quill, customModules, beforeRender = this.config.beforeRender) {\n    // This function is called each time the editor needs to be rendered,\n    // so it operates individually per component. If no custom module needs to be\n    // registered and no `beforeRender` function is provided, it will emit\n    // immediately and proceed with the rendering.\n    const sources = [this.registerCustomModules(Quill, customModules)];\n    if (beforeRender) {\n      sources.push(beforeRender());\n    }\n    return forkJoin(sources).pipe(map(() => Quill));\n  }\n  /** @internal */\n  registerCustomModules(Quill, customModules, suppressGlobalRegisterWarning) {\n    if (!Array.isArray(customModules)) {\n      return of(Quill);\n    }\n    const sources = [];\n    for (const customModule of customModules) {\n      const {\n        path,\n        implementation: maybeImplementation\n      } = customModule;\n      // If the module is already registered, proceed to the next module...\n      if (this.registeredModules.has(path)) {\n        continue;\n      }\n      this.registeredModules.add(path);\n      if (isObservable(maybeImplementation)) {\n        // If the implementation is an observable, we will wait for it to load and\n        // then register it with Quill. The caller will wait until the module is registered.\n        sources.push(maybeImplementation.pipe(tap(implementation => {\n          Quill.register(path, implementation, suppressGlobalRegisterWarning);\n        })));\n      } else {\n        Quill.register(path, maybeImplementation, suppressGlobalRegisterWarning);\n      }\n    }\n    return sources.length > 0 ? forkJoin(sources).pipe(map(() => Quill)) : of(Quill);\n  }\n  static {\n    this.ɵfac = function QuillService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuillService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: QuillService,\n      factory: QuillService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass QuillEditorBase {\n  constructor() {\n    this.format = input(undefined);\n    this.theme = input(undefined);\n    this.modules = input(undefined);\n    this.debug = input(false);\n    this.readOnly = input(false);\n    this.placeholder = input(undefined);\n    this.maxLength = input(undefined);\n    this.minLength = input(undefined);\n    this.required = input(false);\n    this.formats = input(undefined);\n    this.customToolbarPosition = input('top');\n    this.sanitize = input(undefined);\n    this.beforeRender = input(undefined);\n    this.styles = input(null);\n    this.registry = input(undefined);\n    this.bounds = input(undefined);\n    this.customOptions = input([]);\n    this.customModules = input([]);\n    this.trackChanges = input(undefined);\n    this.classes = input(undefined);\n    this.trimOnValidation = input(false);\n    this.linkPlaceholder = input(undefined);\n    this.compareValues = input(false);\n    this.filterNull = input(false);\n    this.debounceTime = input(undefined);\n    /*\n    https://github.com/KillerCodeMonkey/ngx-quill/issues/1257 - fix null value set\n           provide default empty value\n    by default null\n           e.g. defaultEmptyValue=\"\" - empty string\n           <quill-editor\n      defaultEmptyValue=\"\"\n      formControlName=\"message\"\n    ></quill-editor>\n    */\n    this.defaultEmptyValue = input(null);\n    this.onEditorCreated = new EventEmitter();\n    this.onEditorChanged = new EventEmitter();\n    this.onContentChanged = new EventEmitter();\n    this.onSelectionChanged = new EventEmitter();\n    this.onFocus = new EventEmitter();\n    this.onBlur = new EventEmitter();\n    this.onNativeFocus = new EventEmitter();\n    this.onNativeBlur = new EventEmitter();\n    this.disabled = false; // used to store initial value before ViewInit\n    this.toolbarPosition = signal('top');\n    this.eventsSubscription = null;\n    this.quillSubscription = null;\n    this.elementRef = inject(ElementRef);\n    this.cd = inject(ChangeDetectorRef);\n    this.domSanitizer = inject(DomSanitizer);\n    this.platformId = inject(PLATFORM_ID);\n    this.renderer = inject(Renderer2);\n    this.zone = inject(NgZone);\n    this.service = inject(QuillService);\n    this.destroyRef = inject(DestroyRef);\n    this.valueGetter = input(quillEditor => {\n      let html = quillEditor.getSemanticHTML();\n      if (this.isEmptyValue(html)) {\n        html = this.defaultEmptyValue();\n      }\n      let modelValue = html;\n      const format = getFormat(this.format(), this.service.config.format);\n      if (format === 'text') {\n        modelValue = quillEditor.getText();\n      } else if (format === 'object') {\n        modelValue = quillEditor.getContents();\n      } else if (format === 'json') {\n        try {\n          modelValue = JSON.stringify(quillEditor.getContents());\n        } catch {\n          modelValue = quillEditor.getText();\n        }\n      }\n      return modelValue;\n    });\n    this.valueSetter = input((quillEditor, value) => {\n      const format = getFormat(this.format(), this.service.config.format);\n      if (format === 'html') {\n        const sanitize = [true, false].includes(this.sanitize()) ? this.sanitize() : this.service.config.sanitize || false;\n        if (sanitize) {\n          value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n        }\n        return quillEditor.clipboard.convert({\n          html: value\n        });\n      } else if (format === 'json') {\n        try {\n          return JSON.parse(value);\n        } catch {\n          return [{\n            insert: value\n          }];\n        }\n      }\n      return value;\n    });\n    this.selectionChangeHandler = (range, oldRange, source) => {\n      const trackChanges = this.trackChanges() || this.service.config.trackChanges;\n      const shouldTriggerOnModelTouched = !range && !!this.onModelTouched && (source === 'user' || trackChanges && trackChanges === 'all');\n      // only emit changes when there's any listener\n      if (!this.onBlur.observed && !this.onFocus.observed && !this.onSelectionChanged.observed && !shouldTriggerOnModelTouched) {\n        return;\n      }\n      this.zone.run(() => {\n        if (range === null) {\n          this.onBlur.emit({\n            editor: this.quillEditor,\n            source\n          });\n        } else if (oldRange === null) {\n          this.onFocus.emit({\n            editor: this.quillEditor,\n            source\n          });\n        }\n        this.onSelectionChanged.emit({\n          editor: this.quillEditor,\n          oldRange,\n          range,\n          source\n        });\n        if (shouldTriggerOnModelTouched) {\n          this.onModelTouched();\n        }\n        this.cd.markForCheck();\n      });\n    };\n    this.textChangeHandler = (delta, oldDelta, source) => {\n      // only emit changes emitted by user interactions\n      const text = this.quillEditor.getText();\n      const content = this.quillEditor.getContents();\n      let html = this.quillEditor.getSemanticHTML();\n      if (this.isEmptyValue(html)) {\n        html = this.defaultEmptyValue();\n      }\n      const trackChanges = this.trackChanges() || this.service.config.trackChanges;\n      const shouldTriggerOnModelChange = (source === 'user' || trackChanges && trackChanges === 'all') && !!this.onModelChange;\n      // only emit changes when there's any listener\n      if (!this.onContentChanged.observed && !shouldTriggerOnModelChange) {\n        return;\n      }\n      this.zone.run(() => {\n        if (shouldTriggerOnModelChange) {\n          const valueGetter = this.valueGetter();\n          this.onModelChange(valueGetter(this.quillEditor));\n        }\n        this.onContentChanged.emit({\n          content,\n          delta,\n          editor: this.quillEditor,\n          html,\n          oldDelta,\n          source,\n          text\n        });\n        this.cd.markForCheck();\n      });\n    };\n    this.editorChangeHandler = (event, current, old, source) => {\n      // only emit changes when there's any listener\n      if (!this.onEditorChanged.observed) {\n        return;\n      }\n      // only emit changes emitted by user interactions\n      if (event === 'text-change') {\n        const text = this.quillEditor.getText();\n        const content = this.quillEditor.getContents();\n        let html = this.quillEditor.getSemanticHTML();\n        if (this.isEmptyValue(html)) {\n          html = this.defaultEmptyValue();\n        }\n        this.zone.run(() => {\n          this.onEditorChanged.emit({\n            content,\n            delta: current,\n            editor: this.quillEditor,\n            event,\n            html,\n            oldDelta: old,\n            source,\n            text\n          });\n          this.cd.markForCheck();\n        });\n      } else {\n        this.zone.run(() => {\n          this.onEditorChanged.emit({\n            editor: this.quillEditor,\n            event,\n            oldRange: old,\n            range: current,\n            source\n          });\n          this.cd.markForCheck();\n        });\n      }\n    };\n    this.destroyRef.onDestroy(() => {\n      this.dispose();\n      this.quillSubscription?.unsubscribe();\n      this.quillSubscription = null;\n    });\n  }\n  static normalizeClassNames(classes) {\n    const classList = classes.trim().split(' ');\n    return classList.reduce((prev, cur) => {\n      const trimmed = cur.trim();\n      if (trimmed) {\n        prev.push(trimmed);\n      }\n      return prev;\n    }, []);\n  }\n  ngOnInit() {\n    this.toolbarPosition.set(this.customToolbarPosition());\n  }\n  ngAfterViewInit() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    // The `quill-editor` component might be destroyed before the `quill` chunk is loaded and its code is executed\n    // this will lead to runtime exceptions, since the code will be executed on DOM nodes that don't exist within the tree.\n    this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => this.service.beforeRender(Quill, this.customModules(), this.beforeRender()))).subscribe(Quill => {\n      this.editorElem = this.elementRef.nativeElement.querySelector('[quill-editor-element]');\n      const toolbarElem = this.elementRef.nativeElement.querySelector('[quill-editor-toolbar]');\n      const modules = Object.assign({}, this.modules() || this.service.config.modules);\n      if (toolbarElem) {\n        modules.toolbar = toolbarElem;\n      } else if (modules.toolbar === undefined) {\n        modules.toolbar = defaultModules.toolbar;\n      }\n      let placeholder = this.placeholder() !== undefined ? this.placeholder() : this.service.config.placeholder;\n      if (placeholder === undefined) {\n        placeholder = 'Insert text here ...';\n      }\n      const styles = this.styles();\n      if (styles) {\n        Object.keys(styles).forEach(key => {\n          this.renderer.setStyle(this.editorElem, key, styles[key]);\n        });\n      }\n      if (this.classes()) {\n        this.addClasses(this.classes());\n      }\n      this.customOptions().forEach(customOption => {\n        const newCustomOption = Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        Quill.register(newCustomOption, true);\n      });\n      let bounds = this.bounds() && this.bounds() === 'self' ? this.editorElem : this.bounds();\n      if (!bounds) {\n        // Can use global `document` because we execute this only in the browser.\n        bounds = this.service.config.bounds ? this.service.config.bounds : document.body;\n      }\n      let debug = this.debug();\n      if (!debug && debug !== false && this.service.config.debug) {\n        debug = this.service.config.debug;\n      }\n      let readOnly = this.readOnly();\n      if (!readOnly && this.readOnly() !== false) {\n        readOnly = this.service.config.readOnly !== undefined ? this.service.config.readOnly : false;\n      }\n      let formats = this.formats();\n      if (!formats && formats === undefined) {\n        formats = this.service.config.formats ? [...this.service.config.formats] : this.service.config.formats === null ? null : undefined;\n      }\n      this.zone.runOutsideAngular(() => {\n        this.quillEditor = new Quill(this.editorElem, {\n          bounds,\n          debug,\n          formats,\n          modules,\n          placeholder,\n          readOnly,\n          registry: this.registry(),\n          theme: this.theme() || (this.service.config.theme ? this.service.config.theme : 'snow')\n        });\n        if (this.onNativeBlur.observed) {\n          // https://github.com/quilljs/quill/issues/2186#issuecomment-533401328\n          fromEvent(this.quillEditor.scroll.domNode, 'blur').pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => this.onNativeBlur.next({\n            editor: this.quillEditor,\n            source: 'dom'\n          }));\n          // https://github.com/quilljs/quill/issues/2186#issuecomment-803257538\n          const toolbar = this.quillEditor.getModule('toolbar');\n          if (toolbar.container) {\n            fromEvent(toolbar.container, 'mousedown').pipe(takeUntilDestroyed(this.destroyRef)).subscribe(e => e.preventDefault());\n          }\n        }\n        if (this.onNativeFocus.observed) {\n          fromEvent(this.quillEditor.scroll.domNode, 'focus').pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => this.onNativeFocus.next({\n            editor: this.quillEditor,\n            source: 'dom'\n          }));\n        }\n        // Set optional link placeholder, Quill has no native API for it so using workaround\n        if (this.linkPlaceholder()) {\n          const tooltip = this.quillEditor?.theme?.tooltip;\n          const input = tooltip?.root?.querySelector('input[data-link]');\n          if (input?.dataset) {\n            input.dataset.link = this.linkPlaceholder();\n          }\n        }\n      });\n      if (this.content) {\n        const format = getFormat(this.format(), this.service.config.format);\n        if (format === 'text') {\n          this.quillEditor.setText(this.content, 'silent');\n        } else {\n          const valueSetter = this.valueSetter();\n          const newValue = valueSetter(this.quillEditor, this.content);\n          this.quillEditor.setContents(newValue, 'silent');\n        }\n        const history = this.quillEditor.getModule('history');\n        history.clear();\n      }\n      // initialize disabled status based on this.disabled as default value\n      this.setDisabledState();\n      this.addQuillEventListeners();\n      // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n      // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n      if (!this.onEditorCreated.observed && !this.onValidatorChanged) {\n        return;\n      }\n      // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n      // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n      // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n      raf$().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n        if (this.onValidatorChanged) {\n          this.onValidatorChanged();\n        }\n        this.onEditorCreated.emit(this.quillEditor);\n      });\n    });\n  }\n  ngOnChanges(changes) {\n    if (!this.quillEditor) {\n      return;\n    }\n    if (changes.readOnly) {\n      this.quillEditor.enable(!changes.readOnly.currentValue);\n    }\n    if (changes.placeholder) {\n      this.quillEditor.root.dataset.placeholder = changes.placeholder.currentValue;\n    }\n    if (changes.styles) {\n      const currentStyling = changes.styles.currentValue;\n      const previousStyling = changes.styles.previousValue;\n      if (previousStyling) {\n        Object.keys(previousStyling).forEach(key => {\n          this.renderer.removeStyle(this.editorElem, key);\n        });\n      }\n      if (currentStyling) {\n        Object.keys(currentStyling).forEach(key => {\n          this.renderer.setStyle(this.editorElem, key, this.styles()[key]);\n        });\n      }\n    }\n    if (changes.classes) {\n      const currentClasses = changes.classes.currentValue;\n      const previousClasses = changes.classes.previousValue;\n      if (previousClasses) {\n        this.removeClasses(previousClasses);\n      }\n      if (currentClasses) {\n        this.addClasses(currentClasses);\n      }\n    }\n    // We'd want to re-apply event listeners if the `debounceTime` binding changes to apply the\n    // `debounceTime` operator or vice-versa remove it.\n    if (changes.debounceTime) {\n      this.addQuillEventListeners();\n    }\n  }\n  addClasses(classList) {\n    QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n      this.renderer.addClass(this.editorElem, c);\n    });\n  }\n  removeClasses(classList) {\n    QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n      this.renderer.removeClass(this.editorElem, c);\n    });\n  }\n  writeValue(currentValue) {\n    // optional fix for https://github.com/angular/angular/issues/14988\n    if (this.filterNull() && currentValue === null) {\n      return;\n    }\n    this.content = currentValue;\n    if (!this.quillEditor) {\n      return;\n    }\n    const format = getFormat(this.format(), this.service.config.format);\n    const valueSetter = this.valueSetter();\n    const newValue = valueSetter(this.quillEditor, currentValue);\n    if (this.compareValues()) {\n      const currentEditorValue = this.quillEditor.getContents();\n      if (JSON.stringify(currentEditorValue) === JSON.stringify(newValue)) {\n        return;\n      }\n    }\n    if (currentValue) {\n      if (format === 'text') {\n        this.quillEditor.setText(currentValue);\n      } else {\n        this.quillEditor.setContents(newValue);\n      }\n      return;\n    }\n    this.quillEditor.setText('');\n  }\n  setDisabledState(isDisabled = this.disabled) {\n    // store initial value to set appropriate disabled status after ViewInit\n    this.disabled = isDisabled;\n    if (this.quillEditor) {\n      if (isDisabled) {\n        this.quillEditor.disable();\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'disabled', 'disabled');\n      } else {\n        if (!this.readOnly()) {\n          this.quillEditor.enable();\n        }\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'disabled');\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  registerOnValidatorChange(fn) {\n    this.onValidatorChanged = fn;\n  }\n  validate() {\n    if (!this.quillEditor) {\n      return null;\n    }\n    const err = {};\n    let valid = true;\n    const text = this.quillEditor.getText();\n    // trim text if wanted + handle special case that an empty editor contains a new line\n    const textLength = this.trimOnValidation() ? text.trim().length : text.length === 1 && text.trim().length === 0 ? 0 : text.length - 1;\n    const deltaOperations = this.quillEditor.getContents().ops;\n    const onlyEmptyOperation = !!deltaOperations && deltaOperations.length === 1 && ['\\n', ''].includes(deltaOperations[0].insert?.toString());\n    if (this.minLength() && textLength && textLength < this.minLength()) {\n      err.minLengthError = {\n        given: textLength,\n        minLength: this.minLength()\n      };\n      valid = false;\n    }\n    if (this.maxLength() && textLength > this.maxLength()) {\n      err.maxLengthError = {\n        given: textLength,\n        maxLength: this.maxLength()\n      };\n      valid = false;\n    }\n    if (this.required() && !textLength && onlyEmptyOperation) {\n      err.requiredError = {\n        empty: true\n      };\n      valid = false;\n    }\n    return valid ? null : err;\n  }\n  addQuillEventListeners() {\n    this.dispose();\n    // We have to enter the `<root>` zone when adding event listeners, so `debounceTime` will spawn the\n    // `AsyncAction` there w/o triggering change detections. We still re-enter the Angular's zone through\n    // `zone.run` when we emit an event to the parent component.\n    this.zone.runOutsideAngular(() => {\n      this.eventsSubscription = new Subscription();\n      this.eventsSubscription.add(\n      // mark model as touched if editor lost focus\n      fromEvent(this.quillEditor, 'selection-change').subscribe(([range, oldRange, source]) => {\n        this.selectionChangeHandler(range, oldRange, source);\n      }));\n      // The `fromEvent` supports passing JQuery-style event targets, the editor has `on` and `off` methods which\n      // will be invoked upon subscription and teardown.\n      let textChange$ = fromEvent(this.quillEditor, 'text-change');\n      let editorChange$ = fromEvent(this.quillEditor, 'editor-change');\n      if (typeof this.debounceTime() === 'number') {\n        textChange$ = textChange$.pipe(debounceTime(this.debounceTime()));\n        editorChange$ = editorChange$.pipe(debounceTime(this.debounceTime()));\n      }\n      this.eventsSubscription.add(\n      // update model if text changes\n      textChange$.subscribe(([delta, oldDelta, source]) => {\n        this.textChangeHandler(delta, oldDelta, source);\n      }));\n      this.eventsSubscription.add(\n      // triggered if selection or text changed\n      editorChange$.subscribe(([event, current, old, source]) => {\n        this.editorChangeHandler(event, current, old, source);\n      }));\n    });\n  }\n  dispose() {\n    this.eventsSubscription?.unsubscribe();\n    this.eventsSubscription = null;\n  }\n  isEmptyValue(html) {\n    return html === '<p></p>' || html === '<div></div>' || html === '<p><br></p>' || html === '<div><br></div>';\n  }\n  static {\n    this.ɵfac = function QuillEditorBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuillEditorBase)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: QuillEditorBase,\n      inputs: {\n        format: [1, \"format\"],\n        theme: [1, \"theme\"],\n        modules: [1, \"modules\"],\n        debug: [1, \"debug\"],\n        readOnly: [1, \"readOnly\"],\n        placeholder: [1, \"placeholder\"],\n        maxLength: [1, \"maxLength\"],\n        minLength: [1, \"minLength\"],\n        required: [1, \"required\"],\n        formats: [1, \"formats\"],\n        customToolbarPosition: [1, \"customToolbarPosition\"],\n        sanitize: [1, \"sanitize\"],\n        beforeRender: [1, \"beforeRender\"],\n        styles: [1, \"styles\"],\n        registry: [1, \"registry\"],\n        bounds: [1, \"bounds\"],\n        customOptions: [1, \"customOptions\"],\n        customModules: [1, \"customModules\"],\n        trackChanges: [1, \"trackChanges\"],\n        classes: [1, \"classes\"],\n        trimOnValidation: [1, \"trimOnValidation\"],\n        linkPlaceholder: [1, \"linkPlaceholder\"],\n        compareValues: [1, \"compareValues\"],\n        filterNull: [1, \"filterNull\"],\n        debounceTime: [1, \"debounceTime\"],\n        defaultEmptyValue: [1, \"defaultEmptyValue\"],\n        valueGetter: [1, \"valueGetter\"],\n        valueSetter: [1, \"valueSetter\"]\n      },\n      outputs: {\n        onEditorCreated: \"onEditorCreated\",\n        onEditorChanged: \"onEditorChanged\",\n        onContentChanged: \"onContentChanged\",\n        onSelectionChanged: \"onSelectionChanged\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onNativeFocus: \"onNativeFocus\",\n        onNativeBlur: \"onNativeBlur\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillEditorBase, [{\n    type: Directive\n  }], () => [], {\n    onEditorCreated: [{\n      type: Output\n    }],\n    onEditorChanged: [{\n      type: Output\n    }],\n    onContentChanged: [{\n      type: Output\n    }],\n    onSelectionChanged: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onNativeFocus: [{\n      type: Output\n    }],\n    onNativeBlur: [{\n      type: Output\n    }]\n  });\n})();\nclass QuillEditorComponent extends QuillEditorBase {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵQuillEditorComponent_BaseFactory;\n      return function QuillEditorComponent_Factory(__ngFactoryType__) {\n        return (ɵQuillEditorComponent_BaseFactory || (ɵQuillEditorComponent_BaseFactory = i0.ɵɵgetInheritedFactory(QuillEditorComponent)))(__ngFactoryType__ || QuillEditorComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: QuillEditorComponent,\n      selectors: [[\"quill-editor\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }, {\n        multi: true,\n        provide: NG_VALIDATORS,\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 2,\n      consts: [[\"quill-editor-element\", \"\"]],\n      template: function QuillEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵtemplate(0, QuillEditorComponent_Conditional_0_Template, 1, 0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵtemplate(4, QuillEditorComponent_Conditional_4_Template, 1, 0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.toolbarPosition() !== \"top\" ? 0 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx.toolbarPosition() === \"top\" ? 4 : -1);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{display:inline-block}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillEditorComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.Emulated,\n      providers: [{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }, {\n        multi: true,\n        provide: NG_VALIDATORS,\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }],\n      selector: 'quill-editor',\n      template: `\n    @if (toolbarPosition() !== 'top') {\n        <div quill-editor-element></div>\n    }\n\n    <ng-content select=\"[above-quill-editor-toolbar]\"></ng-content>\n    <ng-content select=\"[quill-editor-toolbar]\"></ng-content>\n    <ng-content select=\"[below-quill-editor-toolbar]\"></ng-content>\n\n    @if (toolbarPosition() === 'top') {\n        <div quill-editor-element></div>\n    }\n  `,\n      styles: [\":host{display:inline-block}\\n\"]\n    }]\n  }], null, null);\n})();\nclass QuillViewHTMLComponent {\n  constructor() {\n    this.content = input('');\n    this.theme = input(undefined);\n    this.sanitize = input(undefined);\n    this.innerHTML = signal('');\n    this.themeClass = signal('ql-snow');\n    this.sanitizer = inject(DomSanitizer);\n    this.service = inject(QuillService);\n  }\n  ngOnChanges(changes) {\n    if (changes.theme) {\n      const theme = changes.theme.currentValue || (this.service.config.theme ? this.service.config.theme : 'snow');\n      this.themeClass.set(`ql-${theme} ngx-quill-view-html`);\n    } else if (!this.theme()) {\n      const theme = this.service.config.theme ? this.service.config.theme : 'snow';\n      this.themeClass.set(`ql-${theme} ngx-quill-view-html`);\n    }\n    if (changes.content) {\n      const content = changes.content.currentValue;\n      const sanitize = [true, false].includes(this.sanitize()) ? this.sanitize() : this.service.config.sanitize || false;\n      const innerHTML = sanitize ? content : this.sanitizer.bypassSecurityTrustHtml(content);\n      this.innerHTML.set(innerHTML);\n    }\n  }\n  static {\n    this.ɵfac = function QuillViewHTMLComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuillViewHTMLComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: QuillViewHTMLComponent,\n      selectors: [[\"quill-view-html\"]],\n      inputs: {\n        content: [1, \"content\"],\n        theme: [1, \"theme\"],\n        sanitize: [1, \"sanitize\"]\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 2,\n      vars: 3,\n      consts: [[1, \"ql-container\"], [1, \"ql-editor\", 3, \"innerHTML\"]],\n      template: function QuillViewHTMLComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.themeClass());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"innerHTML\", ctx.innerHTML(), i0.ɵɵsanitizeHtml);\n        }\n      },\n      styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillViewHTMLComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'quill-view-html',\n      template: `\n  <div class=\"ql-container\" [class]=\"themeClass()\">\n    <div class=\"ql-editor\" [innerHTML]=\"innerHTML()\">\n    </div>\n  </div>\n`,\n      styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"]\n    }]\n  }], null, null);\n})();\nclass QuillViewComponent {\n  constructor() {\n    this.format = input(undefined);\n    this.theme = input(undefined);\n    this.modules = input(undefined);\n    this.debug = input(false);\n    this.formats = input(undefined);\n    this.sanitize = input(undefined);\n    this.beforeRender = input();\n    this.strict = input(true);\n    this.content = input();\n    this.customModules = input([]);\n    this.customOptions = input([]);\n    this.onEditorCreated = new EventEmitter();\n    this.elementRef = inject(ElementRef);\n    this.renderer = inject(Renderer2);\n    this.ngZone = inject(NgZone);\n    this.service = inject(QuillService);\n    this.sanitizer = inject(DomSanitizer);\n    this.platformId = inject(PLATFORM_ID);\n    this.destroyRef = inject(DestroyRef);\n    this.valueSetter = (quillEditor, value) => {\n      const format = getFormat(this.format(), this.service.config.format);\n      let content = value;\n      if (format === 'text') {\n        quillEditor.setText(content);\n      } else {\n        if (format === 'html') {\n          const sanitize = [true, false].includes(this.sanitize()) ? this.sanitize() : this.service.config.sanitize || false;\n          if (sanitize) {\n            value = this.sanitizer.sanitize(SecurityContext.HTML, value);\n          }\n          content = quillEditor.clipboard.convert({\n            html: value\n          });\n        } else if (format === 'json') {\n          try {\n            content = JSON.parse(value);\n          } catch {\n            content = [{\n              insert: value\n            }];\n          }\n        }\n        quillEditor.setContents(content);\n      }\n    };\n  }\n  ngOnChanges(changes) {\n    if (!this.quillEditor) {\n      return;\n    }\n    if (changes.content) {\n      this.valueSetter(this.quillEditor, changes.content.currentValue);\n    }\n  }\n  ngAfterViewInit() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    const quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => this.service.beforeRender(Quill, this.customModules(), this.beforeRender()))).subscribe(Quill => {\n      const modules = Object.assign({}, this.modules() || this.service.config.modules);\n      modules.toolbar = false;\n      this.customOptions().forEach(customOption => {\n        const newCustomOption = Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        Quill.register(newCustomOption, true);\n      });\n      let debug = this.debug();\n      if (!debug && debug !== false && this.service.config.debug) {\n        debug = this.service.config.debug;\n      }\n      let formats = this.formats();\n      if (formats === undefined) {\n        formats = this.service.config.formats ? [...this.service.config.formats] : this.service.config.formats === null ? null : undefined;\n      }\n      const theme = this.theme() || (this.service.config.theme ? this.service.config.theme : 'snow');\n      this.editorElem = this.elementRef.nativeElement.querySelector('[quill-view-element]');\n      this.ngZone.runOutsideAngular(() => {\n        this.quillEditor = new Quill(this.editorElem, {\n          debug,\n          formats,\n          modules,\n          readOnly: true,\n          strict: this.strict(),\n          theme\n        });\n      });\n      this.renderer.addClass(this.editorElem, 'ngx-quill-view');\n      if (this.content()) {\n        this.valueSetter(this.quillEditor, this.content());\n      }\n      // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n      // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n      if (!this.onEditorCreated.observed) {\n        return;\n      }\n      // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n      // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n      // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n      raf$().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n        this.onEditorCreated.emit(this.quillEditor);\n      });\n    });\n    this.destroyRef.onDestroy(() => quillSubscription.unsubscribe());\n  }\n  static {\n    this.ɵfac = function QuillViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuillViewComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: QuillViewComponent,\n      selectors: [[\"quill-view\"]],\n      inputs: {\n        format: [1, \"format\"],\n        theme: [1, \"theme\"],\n        modules: [1, \"modules\"],\n        debug: [1, \"debug\"],\n        formats: [1, \"formats\"],\n        sanitize: [1, \"sanitize\"],\n        beforeRender: [1, \"beforeRender\"],\n        strict: [1, \"strict\"],\n        content: [1, \"content\"],\n        customModules: [1, \"customModules\"],\n        customOptions: [1, \"customOptions\"]\n      },\n      outputs: {\n        onEditorCreated: \"onEditorCreated\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"quill-view-element\", \"\"]],\n      template: function QuillViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n        }\n      },\n      styles: [\".ql-container.ngx-quill-view{border:0}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillViewComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'quill-view',\n      template: `\n  <div quill-view-element></div>\n`,\n      styles: [\".ql-container.ngx-quill-view{border:0}\\n\"]\n    }]\n  }], null, {\n    onEditorCreated: [{\n      type: Output\n    }]\n  });\n})();\nclass QuillModule {\n  static forRoot(config) {\n    return {\n      ngModule: QuillModule,\n      providers: [{\n        provide: QUILL_CONFIG_TOKEN,\n        useValue: config\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function QuillModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuillModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: QuillModule,\n      imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n      exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillModule, [{\n    type: NgModule,\n    args: [{\n      imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n      exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-quill\n */\n// Re-export everything from the secondary entry-point so we can be backwards-compatible\n// and don't introduce breaking changes for consumers.\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QuillEditorBase, QuillEditorComponent, QuillModule, QuillService, QuillViewComponent, QuillViewHTMLComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IAAC,CAAC,QAAQ,UAAU,aAAa,QAAQ;AAAA;AAAA,IAElD,CAAC,cAAc,YAAY;AAAA,IAAG,CAAC;AAAA,MAC7B,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IAAG,CAAC;AAAA,MACH,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,WAAW;AAAA,IACb,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,MAAM,CAAC,SAAS,OAAO,SAAS,MAAM;AAAA,IACxC,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK;AAAA,IAClC,CAAC;AAAA,IAAG,CAAC;AAAA,MACH,OAAO,CAAC;AAAA,IACV,GAAG;AAAA,MACD,YAAY,CAAC;AAAA,IACf,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,MAAM,CAAC;AAAA,IACT,CAAC;AAAA,IAAG,CAAC;AAAA,MACH,OAAO,CAAC;AAAA,IACV,CAAC;AAAA,IAAG,CAAC,OAAO;AAAA;AAAA,IAEZ,CAAC,QAAQ,SAAS,OAAO;AAAA;AAAA,IAEzB,CAAC,OAAO;AAAA,EAAC;AACX;AACA,IAAM,qBAAqB,IAAI,eAAe,UAAU;AAAA,EACtD,YAAY;AAAA,EACZ,SAAS,OAAO;AAAA,IACd,SAAS;AAAA,EACX;AACF,CAAC;AAQD,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,QAAQ,QAAQ;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,qBAAqB,YAAU,yBAAyB,CAAC;AAAA,EAC7D,SAAS;AAAA,EACT,UAAU;AACZ,CAAC,CAAC;;;ACvFF,SAAS,mBAAmB,YAAY;AACtC,MAAI,CAAC,YAAY;AACf,6BAAyB,kBAAkB;AAC3C,iBAAa,OAAO,UAAU;AAAA,EAChC;AACA,QAAM,aAAa,IAAI,WAAW,cAAY;AAC5C,UAAM,eAAe,WAAW,UAAU,SAAS,KAAK,KAAK,QAAQ,CAAC;AACtE,WAAO;AAAA,EACT,CAAC;AACD,SAAO,YAAU;AACf,WAAO,OAAO,KAAK,UAAU,UAAU,CAAC;AAAA,EAC1C;AACF;;;AC/BA,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;AACnI,IAAM,MAAM,CAAC,gCAAgC,0BAA0B,8BAA8B;AACrG,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AAUA,IAAM,YAAY,CAAC,QAAQ,iBAAiB;AAC1C,QAAM,eAAe,UAAU;AAC/B,SAAO,gBAAgB;AACzB;AACA,IAAM,OAAO,MAAM;AACjB,SAAO,IAAI,WAAW,gBAAc;AAClC,UAAM,QAAQ,sBAAsB,MAAM;AACxC,iBAAW,KAAK;AAChB,iBAAW,SAAS;AAAA,IACtB,CAAC;AACD,WAAO,MAAM,qBAAqB,KAAK;AAAA,EACzC,CAAC;AACH;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,SAAS,OAAO,kBAAkB,KAAK;AAAA,MAC1C,SAAS;AAAA,IACX;AACA,SAAK,SAAS,MAAM,MAAY;AAC9B,UAAI,CAAC,KAAK,OAAO;AAQf,cAAM,+BAA+B,SAAS;AAW9C,iBAAS,mBAAmB,SAAS,iCAAiC,KAAK,SAAS;AACpF,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,MAAM,OAAO,wCAAgC;AACjD,iBAAS,mBAAmB;AAC5B,aAAK,QAAQ;AAAA,MACf;AAEA,WAAK,OAAO,eAAe,QAAQ,kBAAgB;AACjD,cAAM,kBAAkB,KAAK,MAAM,OAAO,aAAa,MAAM;AAC7D,wBAAgB,YAAY,aAAa;AACzC,aAAK,MAAM,SAAS,iBAAiB,MAAM,KAAK,OAAO,6BAA6B;AAAA,MACtF,CAAC;AAED,aAAO,IAAI,QAAQ,aAAW;AAC5B,aAAK,sBAAsB,KAAK,OAAO,KAAK,OAAO,eAAe,KAAK,OAAO,6BAA6B,EAAE,UAAU,OAAO;AAAA,MAChI,CAAC;AAAA,IACH,EAAC,EAAE,KAAK,YAAY;AAAA,MAClB,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,CAAC;AAGF,SAAK,oBAAoB,oBAAI,IAAI;AAAA,EACnC;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,OAAO,eAAe,eAAe,KAAK,OAAO,cAAc;AAK1E,UAAM,UAAU,CAAC,KAAK,sBAAsB,OAAO,aAAa,CAAC;AACjE,QAAI,cAAc;AAChB,cAAQ,KAAK,aAAa,CAAC;AAAA,IAC7B;AACA,WAAO,SAAS,OAAO,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC;AAAA,EAChD;AAAA;AAAA,EAEA,sBAAsB,OAAO,eAAe,+BAA+B;AACzE,QAAI,CAAC,MAAM,QAAQ,aAAa,GAAG;AACjC,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,UAAM,UAAU,CAAC;AACjB,eAAW,gBAAgB,eAAe;AACxC,YAAM;AAAA,QACJ;AAAA,QACA,gBAAgB;AAAA,MAClB,IAAI;AAEJ,UAAI,KAAK,kBAAkB,IAAI,IAAI,GAAG;AACpC;AAAA,MACF;AACA,WAAK,kBAAkB,IAAI,IAAI;AAC/B,UAAI,aAAa,mBAAmB,GAAG;AAGrC,gBAAQ,KAAK,oBAAoB,KAAK,IAAI,oBAAkB;AAC1D,gBAAM,SAAS,MAAM,gBAAgB,6BAA6B;AAAA,QACpE,CAAC,CAAC,CAAC;AAAA,MACL,OAAO;AACL,cAAM,SAAS,MAAM,qBAAqB,6BAA6B;AAAA,MACzE;AAAA,IACF;AACA,WAAO,QAAQ,SAAS,IAAI,SAAS,OAAO,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK;AAAA,EACjF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,SAAS,MAAM,MAAS;AAC7B,SAAK,QAAQ,MAAM,MAAS;AAC5B,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,QAAQ,MAAM,KAAK;AACxB,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,cAAc,MAAM,MAAS;AAClC,SAAK,YAAY,MAAM,MAAS;AAChC,SAAK,YAAY,MAAM,MAAS;AAChC,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,wBAAwB,MAAM,KAAK;AACxC,SAAK,WAAW,MAAM,MAAS;AAC/B,SAAK,eAAe,MAAM,MAAS;AACnC,SAAK,SAAS,MAAM,IAAI;AACxB,SAAK,WAAW,MAAM,MAAS;AAC/B,SAAK,SAAS,MAAM,MAAS;AAC7B,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,eAAe,MAAM,MAAS;AACnC,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,mBAAmB,MAAM,KAAK;AACnC,SAAK,kBAAkB,MAAM,MAAS;AACtC,SAAK,gBAAgB,MAAM,KAAK;AAChC,SAAK,aAAa,MAAM,KAAK;AAC7B,SAAK,eAAe,MAAM,MAAS;AAWnC,SAAK,oBAAoB,MAAM,IAAI;AACnC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,WAAW;AAChB,SAAK,kBAAkB,OAAO,KAAK;AACnC,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,aAAa,OAAO,WAAW;AACpC,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,OAAO,OAAO,MAAM;AACzB,SAAK,UAAU,OAAO,YAAY;AAClC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,cAAc,MAAM,iBAAe;AACtC,UAAI,OAAO,YAAY,gBAAgB;AACvC,UAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,eAAO,KAAK,kBAAkB;AAAA,MAChC;AACA,UAAI,aAAa;AACjB,YAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAI,WAAW,QAAQ;AACrB,qBAAa,YAAY,QAAQ;AAAA,MACnC,WAAW,WAAW,UAAU;AAC9B,qBAAa,YAAY,YAAY;AAAA,MACvC,WAAW,WAAW,QAAQ;AAC5B,YAAI;AACF,uBAAa,KAAK,UAAU,YAAY,YAAY,CAAC;AAAA,QACvD,QAAQ;AACN,uBAAa,YAAY,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,cAAc,MAAM,CAAC,aAAa,UAAU;AAC/C,YAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAI,WAAW,QAAQ;AACrB,cAAM,WAAW,CAAC,MAAM,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,YAAY;AAC7G,YAAI,UAAU;AACZ,kBAAQ,KAAK,aAAa,SAAS,gBAAgB,MAAM,KAAK;AAAA,QAChE;AACA,eAAO,YAAY,UAAU,QAAQ;AAAA,UACnC,MAAM;AAAA,QACR,CAAC;AAAA,MACH,WAAW,WAAW,QAAQ;AAC5B,YAAI;AACF,iBAAO,KAAK,MAAM,KAAK;AAAA,QACzB,QAAQ;AACN,iBAAO,CAAC;AAAA,YACN,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,yBAAyB,CAAC,OAAO,UAAU,WAAW;AACzD,YAAM,eAAe,KAAK,aAAa,KAAK,KAAK,QAAQ,OAAO;AAChE,YAAM,8BAA8B,CAAC,SAAS,CAAC,CAAC,KAAK,mBAAmB,WAAW,UAAU,gBAAgB,iBAAiB;AAE9H,UAAI,CAAC,KAAK,OAAO,YAAY,CAAC,KAAK,QAAQ,YAAY,CAAC,KAAK,mBAAmB,YAAY,CAAC,6BAA6B;AACxH;AAAA,MACF;AACA,WAAK,KAAK,IAAI,MAAM;AAClB,YAAI,UAAU,MAAM;AAClB,eAAK,OAAO,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH,WAAW,aAAa,MAAM;AAC5B,eAAK,QAAQ,KAAK;AAAA,YAChB,QAAQ,KAAK;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH;AACA,aAAK,mBAAmB,KAAK;AAAA,UAC3B,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,6BAA6B;AAC/B,eAAK,eAAe;AAAA,QACtB;AACA,aAAK,GAAG,aAAa;AAAA,MACvB,CAAC;AAAA,IACH;AACA,SAAK,oBAAoB,CAAC,OAAO,UAAU,WAAW;AAEpD,YAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,YAAM,UAAU,KAAK,YAAY,YAAY;AAC7C,UAAI,OAAO,KAAK,YAAY,gBAAgB;AAC5C,UAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,eAAO,KAAK,kBAAkB;AAAA,MAChC;AACA,YAAM,eAAe,KAAK,aAAa,KAAK,KAAK,QAAQ,OAAO;AAChE,YAAM,8BAA8B,WAAW,UAAU,gBAAgB,iBAAiB,UAAU,CAAC,CAAC,KAAK;AAE3G,UAAI,CAAC,KAAK,iBAAiB,YAAY,CAAC,4BAA4B;AAClE;AAAA,MACF;AACA,WAAK,KAAK,IAAI,MAAM;AAClB,YAAI,4BAA4B;AAC9B,gBAAM,cAAc,KAAK,YAAY;AACrC,eAAK,cAAc,YAAY,KAAK,WAAW,CAAC;AAAA,QAClD;AACA,aAAK,iBAAiB,KAAK;AAAA,UACzB;AAAA,UACA;AAAA,UACA,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,GAAG,aAAa;AAAA,MACvB,CAAC;AAAA,IACH;AACA,SAAK,sBAAsB,CAAC,OAAO,SAAS,KAAK,WAAW;AAE1D,UAAI,CAAC,KAAK,gBAAgB,UAAU;AAClC;AAAA,MACF;AAEA,UAAI,UAAU,eAAe;AAC3B,cAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,cAAM,UAAU,KAAK,YAAY,YAAY;AAC7C,YAAI,OAAO,KAAK,YAAY,gBAAgB;AAC5C,YAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,iBAAO,KAAK,kBAAkB;AAAA,QAChC;AACA,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,gBAAgB,KAAK;AAAA,YACxB;AAAA,YACA,OAAO;AAAA,YACP,QAAQ,KAAK;AAAA,YACb;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,UACF,CAAC;AACD,eAAK,GAAG,aAAa;AAAA,QACvB,CAAC;AAAA,MACH,OAAO;AACL,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,gBAAgB,KAAK;AAAA,YACxB,QAAQ,KAAK;AAAA,YACb;AAAA,YACA,UAAU;AAAA,YACV,OAAO;AAAA,YACP;AAAA,UACF,CAAC;AACD,eAAK,GAAG,aAAa;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,WAAW,UAAU,MAAM;AAC9B,WAAK,QAAQ;AACb,WAAK,mBAAmB,YAAY;AACpC,WAAK,oBAAoB;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,OAAO,oBAAoB,SAAS;AAClC,UAAM,YAAY,QAAQ,KAAK,EAAE,MAAM,GAAG;AAC1C,WAAO,UAAU,OAAO,CAAC,MAAM,QAAQ;AACrC,YAAM,UAAU,IAAI,KAAK;AACzB,UAAI,SAAS;AACX,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAAA,EACvD;AAAA,EACA,kBAAkB;AAChB,QAAI,iBAAiB,KAAK,UAAU,GAAG;AACrC;AAAA,IACF;AAGA,SAAK,oBAAoB,KAAK,QAAQ,SAAS,EAAE,KAAK,SAAS,WAAS,KAAK,QAAQ,aAAa,OAAO,KAAK,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,WAAS;AACvK,WAAK,aAAa,KAAK,WAAW,cAAc,cAAc,wBAAwB;AACtF,YAAM,cAAc,KAAK,WAAW,cAAc,cAAc,wBAAwB;AACxF,YAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,OAAO;AAC/E,UAAI,aAAa;AACf,gBAAQ,UAAU;AAAA,MACpB,WAAW,QAAQ,YAAY,QAAW;AACxC,gBAAQ,UAAU,eAAe;AAAA,MACnC;AACA,UAAI,cAAc,KAAK,YAAY,MAAM,SAAY,KAAK,YAAY,IAAI,KAAK,QAAQ,OAAO;AAC9F,UAAI,gBAAgB,QAAW;AAC7B,sBAAc;AAAA,MAChB;AACA,YAAM,SAAS,KAAK,OAAO;AAC3B,UAAI,QAAQ;AACV,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,eAAK,SAAS,SAAS,KAAK,YAAY,KAAK,OAAO,GAAG,CAAC;AAAA,QAC1D,CAAC;AAAA,MACH;AACA,UAAI,KAAK,QAAQ,GAAG;AAClB,aAAK,WAAW,KAAK,QAAQ,CAAC;AAAA,MAChC;AACA,WAAK,cAAc,EAAE,QAAQ,kBAAgB;AAC3C,cAAM,kBAAkB,MAAM,OAAO,aAAa,MAAM;AACxD,wBAAgB,YAAY,aAAa;AACzC,cAAM,SAAS,iBAAiB,IAAI;AAAA,MACtC,CAAC;AACD,UAAI,SAAS,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,SAAS,KAAK,aAAa,KAAK,OAAO;AACvF,UAAI,CAAC,QAAQ;AAEX,iBAAS,KAAK,QAAQ,OAAO,SAAS,KAAK,QAAQ,OAAO,SAAS,SAAS;AAAA,MAC9E;AACA,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,CAAC,SAAS,UAAU,SAAS,KAAK,QAAQ,OAAO,OAAO;AAC1D,gBAAQ,KAAK,QAAQ,OAAO;AAAA,MAC9B;AACA,UAAI,WAAW,KAAK,SAAS;AAC7B,UAAI,CAAC,YAAY,KAAK,SAAS,MAAM,OAAO;AAC1C,mBAAW,KAAK,QAAQ,OAAO,aAAa,SAAY,KAAK,QAAQ,OAAO,WAAW;AAAA,MACzF;AACA,UAAI,UAAU,KAAK,QAAQ;AAC3B,UAAI,CAAC,WAAW,YAAY,QAAW;AACrC,kBAAU,KAAK,QAAQ,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,OAAO,IAAI,KAAK,QAAQ,OAAO,YAAY,OAAO,OAAO;AAAA,MAC3H;AACA,WAAK,KAAK,kBAAkB,MAAM;AAChC,aAAK,cAAc,IAAI,MAAM,KAAK,YAAY;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,KAAK,SAAS;AAAA,UACxB,OAAO,KAAK,MAAM,MAAM,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AAAA,QAClF,CAAC;AACD,YAAI,KAAK,aAAa,UAAU;AAE9B,oBAAU,KAAK,YAAY,OAAO,SAAS,MAAM,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,aAAa,KAAK;AAAA,YAClI,QAAQ,KAAK;AAAA,YACb,QAAQ;AAAA,UACV,CAAC,CAAC;AAEF,gBAAM,UAAU,KAAK,YAAY,UAAU,SAAS;AACpD,cAAI,QAAQ,WAAW;AACrB,sBAAU,QAAQ,WAAW,WAAW,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,OAAK,EAAE,eAAe,CAAC;AAAA,UACvH;AAAA,QACF;AACA,YAAI,KAAK,cAAc,UAAU;AAC/B,oBAAU,KAAK,YAAY,OAAO,SAAS,OAAO,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,cAAc,KAAK;AAAA,YACpI,QAAQ,KAAK;AAAA,YACb,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAEA,YAAI,KAAK,gBAAgB,GAAG;AAC1B,gBAAM,UAAU,KAAK,aAAa,OAAO;AACzC,gBAAMA,SAAQ,SAAS,MAAM,cAAc,kBAAkB;AAC7D,cAAIA,QAAO,SAAS;AAClB,YAAAA,OAAM,QAAQ,OAAO,KAAK,gBAAgB;AAAA,UAC5C;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,KAAK,SAAS;AAChB,cAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,YAAI,WAAW,QAAQ;AACrB,eAAK,YAAY,QAAQ,KAAK,SAAS,QAAQ;AAAA,QACjD,OAAO;AACL,gBAAM,cAAc,KAAK,YAAY;AACrC,gBAAM,WAAW,YAAY,KAAK,aAAa,KAAK,OAAO;AAC3D,eAAK,YAAY,YAAY,UAAU,QAAQ;AAAA,QACjD;AACA,cAAM,UAAU,KAAK,YAAY,UAAU,SAAS;AACpD,gBAAQ,MAAM;AAAA,MAChB;AAEA,WAAK,iBAAiB;AACtB,WAAK,uBAAuB;AAG5B,UAAI,CAAC,KAAK,gBAAgB,YAAY,CAAC,KAAK,oBAAoB;AAC9D;AAAA,MACF;AAIA,WAAK,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC/D,YAAI,KAAK,oBAAoB;AAC3B,eAAK,mBAAmB;AAAA,QAC1B;AACA,aAAK,gBAAgB,KAAK,KAAK,WAAW;AAAA,MAC5C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,UAAU;AACpB,WAAK,YAAY,OAAO,CAAC,QAAQ,SAAS,YAAY;AAAA,IACxD;AACA,QAAI,QAAQ,aAAa;AACvB,WAAK,YAAY,KAAK,QAAQ,cAAc,QAAQ,YAAY;AAAA,IAClE;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,iBAAiB,QAAQ,OAAO;AACtC,YAAM,kBAAkB,QAAQ,OAAO;AACvC,UAAI,iBAAiB;AACnB,eAAO,KAAK,eAAe,EAAE,QAAQ,SAAO;AAC1C,eAAK,SAAS,YAAY,KAAK,YAAY,GAAG;AAAA,QAChD,CAAC;AAAA,MACH;AACA,UAAI,gBAAgB;AAClB,eAAO,KAAK,cAAc,EAAE,QAAQ,SAAO;AACzC,eAAK,SAAS,SAAS,KAAK,YAAY,KAAK,KAAK,OAAO,EAAE,GAAG,CAAC;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,QAAQ,SAAS;AACnB,YAAM,iBAAiB,QAAQ,QAAQ;AACvC,YAAM,kBAAkB,QAAQ,QAAQ;AACxC,UAAI,iBAAiB;AACnB,aAAK,cAAc,eAAe;AAAA,MACpC;AACA,UAAI,gBAAgB;AAClB,aAAK,WAAW,cAAc;AAAA,MAChC;AAAA,IACF;AAGA,QAAI,QAAQ,cAAc;AACxB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,WAAW,WAAW;AACpB,qBAAgB,oBAAoB,SAAS,EAAE,QAAQ,OAAK;AAC1D,WAAK,SAAS,SAAS,KAAK,YAAY,CAAC;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,cAAc,WAAW;AACvB,qBAAgB,oBAAoB,SAAS,EAAE,QAAQ,OAAK;AAC1D,WAAK,SAAS,YAAY,KAAK,YAAY,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,WAAW,cAAc;AAEvB,QAAI,KAAK,WAAW,KAAK,iBAAiB,MAAM;AAC9C;AAAA,IACF;AACA,SAAK,UAAU;AACf,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAM,cAAc,KAAK,YAAY;AACrC,UAAM,WAAW,YAAY,KAAK,aAAa,YAAY;AAC3D,QAAI,KAAK,cAAc,GAAG;AACxB,YAAM,qBAAqB,KAAK,YAAY,YAAY;AACxD,UAAI,KAAK,UAAU,kBAAkB,MAAM,KAAK,UAAU,QAAQ,GAAG;AACnE;AAAA,MACF;AAAA,IACF;AACA,QAAI,cAAc;AAChB,UAAI,WAAW,QAAQ;AACrB,aAAK,YAAY,QAAQ,YAAY;AAAA,MACvC,OAAO;AACL,aAAK,YAAY,YAAY,QAAQ;AAAA,MACvC;AACA;AAAA,IACF;AACA,SAAK,YAAY,QAAQ,EAAE;AAAA,EAC7B;AAAA,EACA,iBAAiB,aAAa,KAAK,UAAU;AAE3C,SAAK,WAAW;AAChB,QAAI,KAAK,aAAa;AACpB,UAAI,YAAY;AACd,aAAK,YAAY,QAAQ;AACzB,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,YAAY,UAAU;AAAA,MAClF,OAAO;AACL,YAAI,CAAC,KAAK,SAAS,GAAG;AACpB,eAAK,YAAY,OAAO;AAAA,QAC1B;AACA,aAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,UAAU;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,CAAC;AACb,QAAI,QAAQ;AACZ,UAAM,OAAO,KAAK,YAAY,QAAQ;AAEtC,UAAM,aAAa,KAAK,iBAAiB,IAAI,KAAK,KAAK,EAAE,SAAS,KAAK,WAAW,KAAK,KAAK,KAAK,EAAE,WAAW,IAAI,IAAI,KAAK,SAAS;AACpI,UAAM,kBAAkB,KAAK,YAAY,YAAY,EAAE;AACvD,UAAM,qBAAqB,CAAC,CAAC,mBAAmB,gBAAgB,WAAW,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,gBAAgB,CAAC,EAAE,QAAQ,SAAS,CAAC;AACzI,QAAI,KAAK,UAAU,KAAK,cAAc,aAAa,KAAK,UAAU,GAAG;AACnE,UAAI,iBAAiB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW,KAAK,UAAU;AAAA,MAC5B;AACA,cAAQ;AAAA,IACV;AACA,QAAI,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU,GAAG;AACrD,UAAI,iBAAiB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW,KAAK,UAAU;AAAA,MAC5B;AACA,cAAQ;AAAA,IACV;AACA,QAAI,KAAK,SAAS,KAAK,CAAC,cAAc,oBAAoB;AACxD,UAAI,gBAAgB;AAAA,QAClB,OAAO;AAAA,MACT;AACA,cAAQ;AAAA,IACV;AACA,WAAO,QAAQ,OAAO;AAAA,EACxB;AAAA,EACA,yBAAyB;AACvB,SAAK,QAAQ;AAIb,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,qBAAqB,IAAI,aAAa;AAC3C,WAAK,mBAAmB;AAAA;AAAA,QAExB,UAAU,KAAK,aAAa,kBAAkB,EAAE,UAAU,CAAC,CAAC,OAAO,UAAU,MAAM,MAAM;AACvF,eAAK,uBAAuB,OAAO,UAAU,MAAM;AAAA,QACrD,CAAC;AAAA,MAAC;AAGF,UAAI,cAAc,UAAU,KAAK,aAAa,aAAa;AAC3D,UAAI,gBAAgB,UAAU,KAAK,aAAa,eAAe;AAC/D,UAAI,OAAO,KAAK,aAAa,MAAM,UAAU;AAC3C,sBAAc,YAAY,KAAK,aAAa,KAAK,aAAa,CAAC,CAAC;AAChE,wBAAgB,cAAc,KAAK,aAAa,KAAK,aAAa,CAAC,CAAC;AAAA,MACtE;AACA,WAAK,mBAAmB;AAAA;AAAA,QAExB,YAAY,UAAU,CAAC,CAAC,OAAO,UAAU,MAAM,MAAM;AACnD,eAAK,kBAAkB,OAAO,UAAU,MAAM;AAAA,QAChD,CAAC;AAAA,MAAC;AACF,WAAK,mBAAmB;AAAA;AAAA,QAExB,cAAc,UAAU,CAAC,CAAC,OAAO,SAAS,KAAK,MAAM,MAAM;AACzD,eAAK,oBAAoB,OAAO,SAAS,KAAK,MAAM;AAAA,QACtD,CAAC;AAAA,MAAC;AAAA,IACJ,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,SAAK,oBAAoB,YAAY;AACrC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,SAAS,aAAa,SAAS,iBAAiB,SAAS,iBAAiB,SAAS;AAAA,EAC5F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,UAAU,CAAC,GAAG,UAAU;AAAA,QACxB,aAAa,CAAC,GAAG,aAAa;AAAA,QAC9B,WAAW,CAAC,GAAG,WAAW;AAAA,QAC1B,WAAW,CAAC,GAAG,WAAW;AAAA,QAC1B,UAAU,CAAC,GAAG,UAAU;AAAA,QACxB,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,uBAAuB,CAAC,GAAG,uBAAuB;AAAA,QAClD,UAAU,CAAC,GAAG,UAAU;AAAA,QACxB,cAAc,CAAC,GAAG,cAAc;AAAA,QAChC,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,UAAU,CAAC,GAAG,UAAU;AAAA,QACxB,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,eAAe,CAAC,GAAG,eAAe;AAAA,QAClC,eAAe,CAAC,GAAG,eAAe;AAAA,QAClC,cAAc,CAAC,GAAG,cAAc;AAAA,QAChC,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,QACxC,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,QACtC,eAAe,CAAC,GAAG,eAAe;AAAA,QAClC,YAAY,CAAC,GAAG,YAAY;AAAA,QAC5B,cAAc,CAAC,GAAG,cAAc;AAAA,QAChC,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,QAC1C,aAAa,CAAC,GAAG,aAAa;AAAA,QAC9B,aAAa,CAAC,GAAG,aAAa;AAAA,MAChC;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,cAAc;AAAA,MAChB;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAN,MAAM,8BAA6B,gBAAgB;AAAA,EACjD,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,6BAA6B,mBAAmB;AAC9D,gBAAQ,sCAAsC,oCAAuC,sBAAsB,qBAAoB,IAAI,qBAAqB,qBAAoB;AAAA,MAC9K;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,qBAAoB;AAAA,MACpD,GAAG;AAAA,QACD,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,qBAAoB;AAAA,MACpD,CAAC,CAAC,GAAM,0BAA0B;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,wBAAwB,EAAE,CAAC;AAAA,MACrC,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC5E,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC9E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,gBAAgB,MAAM,QAAQ,IAAI,EAAE;AACzD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,gBAAgB,MAAM,QAAQ,IAAI,EAAE;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,wCAAwC;AAAA,IACnD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,oBAAoB;AAAA,MACpD,GAAG;AAAA,QACD,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,oBAAoB;AAAA,MACpD,CAAC;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,QAAQ,CAAC,+BAA+B;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,UAAU,MAAM,EAAE;AACvB,SAAK,QAAQ,MAAM,MAAS;AAC5B,SAAK,WAAW,MAAM,MAAS;AAC/B,SAAK,YAAY,OAAO,EAAE;AAC1B,SAAK,aAAa,OAAO,SAAS;AAClC,SAAK,YAAY,OAAO,YAAY;AACpC,SAAK,UAAU,OAAO,YAAY;AAAA,EACpC;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,OAAO;AACjB,YAAM,QAAQ,QAAQ,MAAM,iBAAiB,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AACrG,WAAK,WAAW,IAAI,MAAM,KAAK,sBAAsB;AAAA,IACvD,WAAW,CAAC,KAAK,MAAM,GAAG;AACxB,YAAM,QAAQ,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AACtE,WAAK,WAAW,IAAI,MAAM,KAAK,sBAAsB;AAAA,IACvD;AACA,QAAI,QAAQ,SAAS;AACnB,YAAM,UAAU,QAAQ,QAAQ;AAChC,YAAM,WAAW,CAAC,MAAM,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,YAAY;AAC7G,YAAM,YAAY,WAAW,UAAU,KAAK,UAAU,wBAAwB,OAAO;AACrF,WAAK,UAAU,IAAI,SAAS;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,QAAQ;AAAA,QACN,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,UAAU,CAAC,GAAG,UAAU;AAAA,MAC1B;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG,WAAW,CAAC;AAAA,MAC9D,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,OAAO,CAAC;AACxB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,WAAW,CAAC;AAC9B,UAAG,UAAU;AACb,UAAG,WAAW,aAAa,IAAI,UAAU,GAAM,cAAc;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,+CAA+C;AAAA,MACxD,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,QAAQ,CAAC,+CAA+C;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,SAAS,MAAM,MAAS;AAC7B,SAAK,QAAQ,MAAM,MAAS;AAC5B,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,QAAQ,MAAM,KAAK;AACxB,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,WAAW,MAAM,MAAS;AAC/B,SAAK,eAAe,MAAM;AAC1B,SAAK,SAAS,MAAM,IAAI;AACxB,SAAK,UAAU,MAAM;AACrB,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,UAAU,OAAO,YAAY;AAClC,SAAK,YAAY,OAAO,YAAY;AACpC,SAAK,aAAa,OAAO,WAAW;AACpC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,cAAc,CAAC,aAAa,UAAU;AACzC,YAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAI,UAAU;AACd,UAAI,WAAW,QAAQ;AACrB,oBAAY,QAAQ,OAAO;AAAA,MAC7B,OAAO;AACL,YAAI,WAAW,QAAQ;AACrB,gBAAM,WAAW,CAAC,MAAM,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,YAAY;AAC7G,cAAI,UAAU;AACZ,oBAAQ,KAAK,UAAU,SAAS,gBAAgB,MAAM,KAAK;AAAA,UAC7D;AACA,oBAAU,YAAY,UAAU,QAAQ;AAAA,YACtC,MAAM;AAAA,UACR,CAAC;AAAA,QACH,WAAW,WAAW,QAAQ;AAC5B,cAAI;AACF,sBAAU,KAAK,MAAM,KAAK;AAAA,UAC5B,QAAQ;AACN,sBAAU,CAAC;AAAA,cACT,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAAA,QACF;AACA,oBAAY,YAAY,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,SAAS;AACnB,WAAK,YAAY,KAAK,aAAa,QAAQ,QAAQ,YAAY;AAAA,IACjE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,iBAAiB,KAAK,UAAU,GAAG;AACrC;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK,QAAQ,SAAS,EAAE,KAAK,SAAS,WAAS,KAAK,QAAQ,aAAa,OAAO,KAAK,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,WAAS;AACxK,YAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,OAAO;AAC/E,cAAQ,UAAU;AAClB,WAAK,cAAc,EAAE,QAAQ,kBAAgB;AAC3C,cAAM,kBAAkB,MAAM,OAAO,aAAa,MAAM;AACxD,wBAAgB,YAAY,aAAa;AACzC,cAAM,SAAS,iBAAiB,IAAI;AAAA,MACtC,CAAC;AACD,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,CAAC,SAAS,UAAU,SAAS,KAAK,QAAQ,OAAO,OAAO;AAC1D,gBAAQ,KAAK,QAAQ,OAAO;AAAA,MAC9B;AACA,UAAI,UAAU,KAAK,QAAQ;AAC3B,UAAI,YAAY,QAAW;AACzB,kBAAU,KAAK,QAAQ,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,OAAO,IAAI,KAAK,QAAQ,OAAO,YAAY,OAAO,OAAO;AAAA,MAC3H;AACA,YAAM,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AACvF,WAAK,aAAa,KAAK,WAAW,cAAc,cAAc,sBAAsB;AACpF,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,cAAc,IAAI,MAAM,KAAK,YAAY;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV,QAAQ,KAAK,OAAO;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,SAAS,SAAS,KAAK,YAAY,gBAAgB;AACxD,UAAI,KAAK,QAAQ,GAAG;AAClB,aAAK,YAAY,KAAK,aAAa,KAAK,QAAQ,CAAC;AAAA,MACnD;AAGA,UAAI,CAAC,KAAK,gBAAgB,UAAU;AAClC;AAAA,MACF;AAIA,WAAK,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC/D,aAAK,gBAAgB,KAAK,KAAK,WAAW;AAAA,MAC5C,CAAC;AAAA,IACH,CAAC;AACD,SAAK,WAAW,UAAU,MAAM,kBAAkB,YAAY,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,QAAQ;AAAA,QACN,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,UAAU,CAAC,GAAG,UAAU;AAAA,QACxB,cAAc,CAAC,GAAG,cAAc;AAAA,QAChC,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,eAAe,CAAC,GAAG,eAAe;AAAA,QAClC,eAAe,CAAC,GAAG,eAAe;AAAA,MACpC;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;AAAA,MACnC,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,OAAO,CAAC;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,0CAA0C;AAAA,MACnD,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA,MAGV,QAAQ,CAAC,0CAA0C;AAAA,IACrD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO,QAAQ,QAAQ;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,MAC1E,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,IAC5E,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,MAC1E,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,IAC5E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["input"]}