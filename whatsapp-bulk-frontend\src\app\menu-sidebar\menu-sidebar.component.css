.sidebar {
    padding: 10px;
    background: #ffffff;
    width: 250px;
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    text-align: left;
}

.input-source-btn {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 15px;
    margin-bottom: 12px;
    font-size: 15px;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    transition: background-color 0.2s;
    color: #333;
}

.input-source-btn .icon {
    width: 20px;
    height: 20px;
    margin-right: 4px;
    fill: #555;
}

.input-source-btn:hover {
    background-color: #4285f4;
    color: white;
    font-weight: 600;
}
.input-source-btn.active {
    background-color: #007bff;
    color: white;
    font-weight: 600;
}

.input-source-btn.active i {
    color: white !important;
}
.input-source-btn:hover i {
    color: white !important;
}

i{
    color: #4285f4;
        margin-right: 10px;
        font-size: 15px;
}


@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        padding: 10px 15px;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
        border-bottom: 1px solid #ddd;
    }

    .sidebar-title {
        width: 100%;
        text-align: center;
        font-size: 16px;
        margin-bottom: 8px;
    }

    .input-source-btn {
        flex: 1 1 calc(50% - 10px);
        font-size: 14px;
        padding: 10px 12px;
        margin-bottom: 8px;
        justify-content: center;
        text-align: center;
    }

    .input-source-btn .icon {
        margin-right: 6px;
    }

    i {
        font-size: 14px;
        margin-right: 6px;
    }
}

@media (max-width: 480px) {
    .input-source-btn {
        flex: 1 1 100%;
        font-size: 14px;
        padding: 10px 14px;
    }

    .sidebar-title {
        font-size: 15px;
    }

    i {
        font-size: 13px;
    } }