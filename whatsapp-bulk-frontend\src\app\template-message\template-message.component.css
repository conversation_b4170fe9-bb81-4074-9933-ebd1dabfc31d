/* Outer modal wrapper (optional fix if it's wrapping the box with shadows or padding) */
.modal-wrapper {
    background: rgba(0, 0, 0, 0.3);
    /* optional if you want a semi-transparent background */
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

/* Modal Container */
.modal-box {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 500px;
    margin: 0 auto;
    font-family: 'Segoe UI', sans-serif;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid #ccc;
}
.modal-title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #333;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    margin: 0;
}

.modal-close-btn {
    background: none;
    border: none;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
}

.modal-close-btn:hover {
    color: #e57373;
}

/* Template Selector */
.template-selector {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 12px;
}

.template-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    padding-left: 2px;
}

/* Template Input */
.template-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    border: 1.5px solid #ccc;
    border-radius: 8px;
    padding: 8px 12px;
    background-color: #fff;
    width: 100%;
}

.template-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 15px;
    background: transparent;
    color: #333;
}

.clear-button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #e53935;
    padding: 0 4px;
}

.dropdown-icon {
    font-size: 16px;
    color: #555;
    cursor: pointer;
    margin-left: 4px;
}

/* Dropdown wrapper */
.dropdown-wrapper {
    padding: 20px 20px 0;
    position: relative;
}

.dropdown-list {
    position: absolute;
    width: 100%;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-height: 180px;
    overflow-y: auto;
    margin-top: 4px;
}

.dropdown-item {
    padding: 10px 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
}

.icon-group {
    display: flex;
    gap: 8px;
}

.icon-btn {
    font-size: 14px;
    cursor: pointer;
    color: #444;
}

.icon-btn:hover {
    color: #d9534f;
}

.create-template {
    padding: 10px 14px;
    color: #1976d2;
    font-weight: 500;
    cursor: pointer;
}

.create-template:hover {
    background-color: #f9f9f9;
}

/* Editor Container */
.editor-container {
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

quill-editor {
    width: 100%;
}

/* Toolbar Footer */
.toolbar-footer {
    padding: 0 20px;
    margin-top: 10px;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toolbar-variable,
button[mat-button] {
    background: none;
    border: none;
    color: #1976d2;
    font-weight: 500;
    cursor: pointer;
}

/* Input Field (for new template name) */
.input-field {
    margin: 20px;
    padding: 12px;
    font-size: 15px;
    border: 1.5px solid #ccc;
    border-radius: 8px;
    width: calc(100% - 40px);
}

/* Button Group */
.action-row {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    padding: 0 20px 20px;
}

.btn-primary {
    background-color: #1976d2;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    border: none;
    cursor: pointer;
    height: 38px;
}

.btn-primary:hover {
    background-color: #125eb4;
}

.btn-secondary {
    background-color: #f1f1f1;
    color: #333;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    border: none;
    cursor: pointer;
    height: 38px;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}