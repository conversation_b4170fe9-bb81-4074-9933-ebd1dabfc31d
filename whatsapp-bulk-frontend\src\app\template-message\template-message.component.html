<!-- WRITE MESSAGE FORM -->
<div *ngIf="!creatingTemplate" class="modal-box">
    <div class="modal-title-bar">
        <h3 class="modal-title">Write Message</h3>
        <button class="modal-close-btn" (click)="closeModal()">×</button>
    </div> <!-- Template Dropdown -->
    <div class="dropdown-wrapper">
        <div class="template-selector">
            <div class="template-input-wrapper">
                <input type="text" placeholder="Select or Create Template" [value]="getSelectedTemplateName()" readonly
                    (click)="toggleDropdown()" class="template-input" [(ngModel)]="selectedTemplateName" />

                <button *ngIf="selectedTemplateName || getSelectedTemplateName()" class="clear-button"
                    (click)="clearTemplate()">×</button>

                <span class="dropdown-icon" (click)="toggleDropdown()">▾</span>
            </div>
        </div>

        <!-- Dropdown List -->
        <div class="dropdown-list" *ngIf="dropdownOpen">
            <div class="dropdown-item" *ngFor="let template of templates" (click)="selectTemplate(template)"
                (mouseenter)="hoveredTemplateId = template.templateId" (mouseleave)="hoveredTemplateId = null">
            
                <span>{{ template.templateName }}</span>
            
                <div class="icon-group" *ngIf="hoveredTemplateId === template.templateId">
                    <span class="icon-btn" (click)="editTemplate(template); $event.stopPropagation()">✎</span>
                    <span class="icon-btn" (click)="deleteTemplate(template.templateId); $event.stopPropagation()">❌</span>
                </div>
            </div>

            <div class="create-template" (click)="startCreatingTemplate()">+ Create Template</div>
        </div>
    </div>

    <!-- Message Editor -->
    <div class="editor-container">
        <quill-editor [(ngModel)]="message" [modules]="editorModules" [styles]="{ height: '200px' }" theme="snow"
            placeholder="Type here..." [ngClass]="{  'is-invalid': showEditorError }">
        </quill-editor>

        <!-- Error Message -->
         <div *ngIf="showEditorError" class="text-danger mt-1">Message is required.</div>

        <!-- + Variables -->
        <div class="toolbar-footer">
            <button mat-button [matMenuTriggerFor]="variableMenu">+ Variables</button>
            <mat-menu #variableMenu="matMenu">
                <button mat-menu-item *ngFor="let col of excelColumns" (click)="insertVariable(col)">
                    {{ col }}
                </button>
            </mat-menu>
            <div class="action-row">
                <button class="btn-primary" (click)="onDone()">Done</button>  </div>
        </div>
    </div>
</div>
<!-- CREATE TEMPLATE FORM -->
<div *ngIf="creatingTemplate" class="modal-box">
    <div class="modal-title-bar">
        <h3 class="modal-title">Create New Template</h3>
        <button class="modal-close-btn" (click)="cancelCreatingTemplate()">×</button>
    </div>      
    <input type="text" class="input-field" placeholder="Template Name" [(ngModel)]="newTemplateName" />

    <div class="editor-container">
        <quill-editor [(ngModel)]="message" [modules]="editorModules" [styles]="{ height: '200px' }" theme="snow"
            placeholder="Type here...">
        </quill-editor>

        <div class="toolbar-footer">
            <button class="toolbar-variable" (click)="insertVariable('{{name}}')">+ Variables</button>
            <div class="action-row">
                <button class="btn-secondary" (click)="cancelCreatingTemplate()">Back</button>
                <button class="btn-primary" (click)="saveNewTemplate()">Save</button>  </div>
        </div>
    </div>

    
</div>
