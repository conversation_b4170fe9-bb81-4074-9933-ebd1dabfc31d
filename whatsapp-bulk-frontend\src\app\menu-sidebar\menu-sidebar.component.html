<div class="sidebar">
  <h3 class="sidebar-title">Input Source</h3>

  <button class="input-source-btn" [ngClass]="{ 'active': activePanel === 'paste' }" (click)="selectPanel('paste')">
    <i class="fa-solid fa-pen"></i>
    Copy/Paste
  </button>

  <button class="input-source-btn" [ngClass]="{ 'active': activePanel === 'excel' }" (click)="selectPanel('excel')">
    <i class="fa-solid fa-file-excel"></i>
    Excel Sheets
  </button>

  <button class="input-source-btn" [ngClass]="{ 'active': activePanel === 'google' }" (click)="selectPanel('google')">
    <i class="fa-brands fa-google-drive"></i>
    Google Sheets
  </button>

  <button class="input-source-btn">
    <i class="fa-solid fa-file-csv"></i>
    CSV, Other Formats
  </button>

  <button class="input-source-btn">
    <i class="fa-regular fa-address-book"></i>
    Saved Contacts
  </button>
</div>