.main-panel {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    max-height: 400px;
    min-height: 400px;
    background: #ffffff;
    padding: 20px;
    width: 490px;
    max-width: 100%;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    font-family: 'Segoe UI', sans-serif;
}
.trash-icon{
    text-align: left;
}
.input-area {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 5px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 42px;
}

.chips-box {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
    flex-grow: 1;
}

.chip {
    background-color: #e9ecef;
    padding: 4px 10px;
    border-radius: 15px;
    display: inline-flex;
    align-items: center;
    font-size: 0.9rem;
}

.remove-chip {
    margin-left: 8px;
    cursor: pointer;
    color: #dc3545;
    font-weight: bold;
}

.cursor-pointer {
    cursor: pointer;
}
.ripple-icon {
    position: relative;
    display: inline-block;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    overflow: hidden;
    transition: background 0.3s;
    color: red;
}

.ripple-icon::after {
    content: "";
    position: absolute;
    background: rgba(255, 0, 0, 0.3);
    /* light red */
    border-radius: 50%;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    transform: scale(0);
    opacity: 0;
    transition: transform 0.4s ease-out, opacity 0.6s ease-out;
}

.ripple-icon:active::after {
    transform: scale(2.5);
    opacity: 1;
}
/* Checkbox Blue Color */
input[type="checkbox"] {
    accent-color: #1a73e8;
    /* Google Blue */
}

/* Optional: Smooth Scrollbar */
.main-panel::-webkit-scrollbar {
    width: 6px;
}

.main-panel::-webkit-scrollbar-thumb {
    background-color: rgba(26, 115, 232, 0.4);
    border-radius: 4px;
}

.form-group,
.form-group-inline {
    margin-bottom: 20px;
}

.form-group-inline {
    display: flex;
    gap: 10px;
    align-items: center;
}

.country-code {
    width: 90px;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.number-input {
    flex: 1;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-weight: normal !important;
    color: #333;
    font-size: 14px;
}

.time-gap-input {
    width: 60px;
    padding: 5px;
    border-radius: 6px;
    border: 1px solid #ccc;
}

.message-box {
    width: 100%;
    height: 120px;
    padding: 12px;
    resize: none;
    border-radius: 10px;
    border: 1px solid #ccc;
    font-size: 14px;
    color: #444;
}

.attachment-options {
    display: flex;
    gap: 25px;
    margin-top: 12px;
    margin-bottom: 10px;
}

.option {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
}

.circle {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    margin-bottom: 5px;
}

.blue {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.gray {
    background-color: #eeeeee;
    color: #9e9e9e;
}

.label {
    font-size: 14px;
    font-weight: 500;
}

.disabled {
    pointer-events: none;
    opacity: 0.6;
}

.button-group {
    position: sticky;
    bottom: -10px;
    /* Instead of 0 */
    background: #ffffff;
    padding: 12px 0;
    display: flex;
    justify-content: space-between;
    z-index: 100;
}

.send-btn {
    padding: 10px 24px;
    font-weight: 600;
    font-size: 15px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

.send-btn:hover {
    background-color: #0c5bd3;
}
.material-icons {
    font-size: 20px;
}
.left{
    text-align: left;
    margin-bottom: 10px;
}

.file-preview {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 14px;
    margin-top: 10px;
    margin-bottom: 15px;

    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    position: relative;
    font-family: 'Segoe UI', sans-serif;
}

.preview-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.preview-left-top {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-icon {
    font-size: 20px;
    color: #1a73e8;
}

.file-name {
    font-weight: 500;
    font-size: 14px;
    color: #333;
}

.file-size {
    color: #666;
    font-size: 13px;
}

.error {
    color: red;
    font-size: 13px;
    margin-top: 4px;
    margin-left: 30px;
    /* indent to align with text after icon */
}

.preview-right {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    height: 100%;
}

.remove-btn {
    font-size: 18px;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.remove-btn:hover {
    color: #d00;
}
.form-group {
    margin-bottom: 15px;
    position: relative;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 6px;
    color: #333;
}

.message-box {
    width: 100%;
    min-height: 100px;
    padding: 10px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #ccc;
    transition: border-color 0.3s;
    resize: vertical;
}

.message-box:focus {
    border-color: #007bff;
    outline: none;
}

.has-error .message-box {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.error-message {
    color: #dc3545;
    font-size: 13px;
    margin-top: 5px;
}
.form-control {
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #ccc;
    transition: border-color 0.3s, box-shadow 0.3s;
    background-color: #fff;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: none;
    outline: none;
}

.is-invalid {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 13px;
    margin-top: 5px;
}

.d-block {
    display: block;
}
.spinner-circle span.spinner-border {
    display: inline-block;
    width: 64px;
    height: 64px;
    border: 8px solid #4285f4;
    border-top: 8px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
.message-settings-row {
    display: flex;
    align-items: center;
    gap: 16px;
    /* space between label and button */
    margin-bottom: 16px;
    /* optional spacing below the row */
}

.custom-button {
    position: relative;
    padding: 10px 36px 10px 16px;
    border: 1px solid #d6d8da;
    background-color: white;
    color: #1976d2;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    min-width: 220px;
    width: 350px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    margin-left: 20px;
}

.clear-inside {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #df2020;
    cursor: pointer;
    user-select: none;
    line-height: 1;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@media (max-width: 600px) {
    .main-panel {
        width: 100%;
        max-width: 100%;
        padding: 15px;
        max-height: 100vh;
        box-shadow: none;
        border-radius: 0;
    }

    .form-group-inline {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .country-code {
        width: 100%;
    }

    .number-input {
        width: 100%;
    }

    .checkbox-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .attachment-options {
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
    }

    .message-box {
        height: 100px;
    }

    .button-group {
        flex-direction: column;
        align-items: stretch;
        padding: 12px 10px;
        gap: 10px;
    }

    .send-btn {
        width: 100%;
        font-size: 16px;
        padding: 12px;
    }

    .chips-box {
        flex-direction: column;
        align-items: flex-start;
    }

    .input-area {
        flex-direction: column;
        align-items: stretch;
        padding: 8px;
    }

    .circle {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .material-icons {
        font-size: 18px;
    }
    .left{
        text-align: left!important;
        justify-content: flex-start!important;
    }
}
