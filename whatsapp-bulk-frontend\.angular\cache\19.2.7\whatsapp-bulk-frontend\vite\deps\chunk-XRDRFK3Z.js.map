{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/unique-selection-dispatcher-a92f7c7f.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/selection-model-a3c34752.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  _listeners = [];\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static ɵfac = function UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UniqueSelectionDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UniqueSelectionDispatcher,\n    factory: UniqueSelectionDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { UniqueSelectionDispatcher as U };\n", "import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n  _multiple;\n  _emitChanges;\n  compareWith;\n  /** Currently-selected values. */\n  _selection = new Set();\n  /** Keeps track of the deselected options that haven't been emitted by the change event. */\n  _deselectedToEmit = [];\n  /** Keeps track of the selected options that haven't been emitted by the change event. */\n  _selectedToEmit = [];\n  /** Cache for the array value of the selected items. */\n  _selected;\n  /** Selected values. */\n  get selected() {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n    return this._selected;\n  }\n  /** Event emitted when the value has changed. */\n  changed = new Subject();\n  constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n    this._multiple = _multiple;\n    this._emitChanges = _emitChanges;\n    this.compareWith = compareWith;\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  select(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  deselect(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  setSelection(...values) {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n    values.forEach(value => this._markSelected(value));\n    oldValues.filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet))).forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  toggle(value) {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  clear(flushEvent = true) {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value) {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty() {\n    return this._selection.size === 0;\n  }\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue() {\n    return !this.isEmpty();\n  }\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate) {\n    if (this._multiple && this.selected) {\n      this._selected.sort(predicate);\n    }\n  }\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n  /** Emits a change event and clears the records of selected and deselected values. */\n  _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit\n      });\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n  /** Selects a value. */\n  _markSelected(value) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n  /** Deselects a value. */\n  _unmarkSelected(value) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n  /** Clears out the selected values. */\n  _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  _verifyValueAssignment(values) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n  /** Whether there are queued up change to be emitted. */\n  _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  _getConcreteValue(inputValue, selection) {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      selection = selection ?? this._selection;\n      for (let selectedValue of selection) {\n        if (this.compareWith(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };\n"], "mappings": ";;;;;;;;;;AAYA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,OAAO,IAAI,MAAM;AACf,aAAS,YAAY,KAAK,YAAY;AACpC,eAAS,IAAI,IAAI;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU;AACf,SAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO,MAAM;AACX,WAAK,aAAa,KAAK,WAAW,OAAO,gBAAc;AACrD,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,IACnC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AClDH,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,oBAAoB,CAAC;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,MAAM,KAAK,KAAK,WAAW,OAAO,CAAC;AAAA,IACtD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,UAAU,IAAI,QAAQ;AAAA,EACtB,YAAY,YAAY,OAAO,yBAAyB,eAAe,MAAM,aAAa;AACxF,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,QAAI,2BAA2B,wBAAwB,QAAQ;AAC7D,UAAI,WAAW;AACb,gCAAwB,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AAAA,MACpE,OAAO;AACL,aAAK,cAAc,wBAAwB,CAAC,CAAC;AAAA,MAC/C;AAEA,WAAK,gBAAgB,SAAS;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,QAAQ;AAClB,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACnD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,QAAQ;AACtB,SAAK,uBAAuB,MAAM;AAClC,UAAM,YAAY,KAAK;AACvB,UAAM,iBAAiB,IAAI,IAAI,OAAO,IAAI,WAAS,KAAK,kBAAkB,KAAK,CAAC,CAAC;AACjF,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,cAAU,OAAO,WAAS,CAAC,eAAe,IAAI,KAAK,kBAAkB,OAAO,cAAc,CAAC,CAAC,EAAE,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AAC1I,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO;AACZ,WAAO,KAAK,WAAW,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,OAAO,KAAK;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,aAAa,MAAM;AACvB,SAAK,WAAW;AAChB,UAAM,UAAU,KAAK,kBAAkB;AACvC,QAAI,YAAY;AACd,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AAChB,WAAO,KAAK,WAAW,IAAI,KAAK,kBAAkB,KAAK,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,WAAO,KAAK,WAAW,SAAS;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,CAAC,KAAK,QAAQ;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,WAAW;AACd,QAAI,KAAK,aAAa,KAAK,UAAU;AACnC,WAAK,UAAU,KAAK,SAAS;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,mBAAmB;AAEjB,SAAK,YAAY;AACjB,QAAI,KAAK,gBAAgB,UAAU,KAAK,kBAAkB,QAAQ;AAChE,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,MAChB,CAAC;AACD,WAAK,oBAAoB,CAAC;AAC1B,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3B,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3B,aAAK,WAAW,IAAI,KAAK;AAAA,MAC3B;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,gBAAgB,KAAK,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,KAAK,WAAW,KAAK,GAAG;AAC1B,WAAK,WAAW,OAAO,KAAK;AAC5B,UAAI,KAAK,cAAc;AACrB,aAAK,kBAAkB,KAAK,KAAK;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,WAAK,WAAW,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,QAAQ;AAC7B,QAAI,OAAO,SAAS,KAAK,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AAC3F,YAAM,wCAAwC;AAAA,IAChD;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,CAAC,EAAE,KAAK,kBAAkB,UAAU,KAAK,gBAAgB;AAAA,EAClE;AAAA;AAAA,EAEA,kBAAkB,YAAY,WAAW;AACvC,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT,OAAO;AACL,kBAAY,aAAa,KAAK;AAC9B,eAAS,iBAAiB,WAAW;AACnC,YAAI,KAAK,YAAY,YAAY,aAAa,GAAG;AAC/C,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAMA,SAAS,0CAA0C;AACjD,SAAO,MAAM,yEAAyE;AACxF;", "names": []}