.report-container {
    max-width: 100%;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    font-family: 'Segoe UI', sans-serif;
}

.report-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
}

.left-align {
    text-align: left;
    margin-bottom: 15px;
}

.btn {
    padding: 8px 16px;
    border: none;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
}

.btn-success {
    background-color: #28a745;
    color: #fff;
}

.btn-success:hover {
    background-color: #218838;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 15px;
}

.report-table th,
.report-table td {
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    text-align: left;
}

.report-table thead {
    background-color: #f8f9fa;
    font-weight: 600;
}

.report-table tbody tr:nth-child(even) {
    background-color: #f6f6f6;
}

.report-table tbody tr:hover {
    background-color: #f1f1f1;
}

.status-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
    color: white;
    text-align: center;
    min-width: 100px;
}

.status-badge.sent {
    background-color: #28a745;
    /* Green for success */
}

.status-badge.failed {
    background-color: #dc3545;
    /* Red for failure */
}

.empty-message {
    text-align: center;
    padding: 20px;
    color: #888;
    font-style: italic;
}